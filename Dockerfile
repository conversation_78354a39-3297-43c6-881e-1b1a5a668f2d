FROM vastbase-g100-single-v1.4-3.0.8.24875-linux-x86_64:20250123144939

# Fix the issue where docker exec -i xxx psql reports command not found
ENV PATH="${PATH}:/home/<USER>/vastbase/bin"

# Ensure your uid/gid exists
# + If the source image does not guarantee uid/gid 1000, it's better to create it yourself
# + Use `RUN ! id 1000 >/dev/null 2>&1 && ...` to avoid errors when user already exists
RUN set -x && \
    ! getent group 1000 >/dev/null 2>&1 && addgroup -g 1000 myappgroup || echo "Group 1000 already exists." && \
    ! getent passwd 1000 >/dev/null 2>&1 && adduser -u 1000 -G myappgroup -D myappuser || echo "User 1000 already exists."

# Copy the wrapper script with execute permissions
COPY --chmod=755 bdtd-entrypoint.sh /home/<USER>/scripts/bdtd-entrypoint.sh

# Set the entrypoint to the new wrapper script
ENTRYPOINT ["/home/<USER>/scripts/bdtd-entrypoint.sh"]
