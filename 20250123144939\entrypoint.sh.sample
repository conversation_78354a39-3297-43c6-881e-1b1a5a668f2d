#!/bin/bash
set -Eeo pipefail 

# usage: file_env VAR [DEFAULT]
#    ie: file_env 'XYZ_DB_PASSWORD' 'example'
# (will allow for "$XYZ_DB_PASSWORD_FILE" to fill in the value of
#  "$XYZ_DB_PASSWORD" from a file, especially for <PERSON><PERSON>'s secrets feature)

#生效环境变量
if [ -f /home/<USER>/.bashrc ];then
    source /home/<USER>/.bashrc
else 
    echo "file does not exist: /home/<USER>/.bashrc"
    exit 2
fi

file_env() {
        local var="$1"
        local fileVar="${var}_FILE"
        local def="${2:-}"
        if [ "${!var:-}" ] && [ "${!fileVar:-}" ]; then
                echo >&2 "error: both $var and $fileVar are set (but are exclusive)"
                exit 1
        fi
        local val="$def"
        if [ "${!var:-}" ]; then
                val="${!var}"
        elif [ "${!fileVar:-}" ]; then
                val="$(< "${!fileVar}")"
        fi
        export "$var"="$val"
        unset "$fileVar"
}

# check to see if this file is being run or sourced from another script
_is_sourced() {
        [ "${#FUNCNAME[@]}" -ge 2 ] \
                && [ "${FUNCNAME[0]}" = '_is_sourced' ] \
                && [ "${FUNCNAME[1]}" = 'source' ]
}

# 目录的创建将会在dockerfil中提前创建好，挂载目录的权限需要使用busybox容器进行修改（k8s启动）或者手动修改（直接docker启动）
# used to create initial vastbase directories and if run as root, ensure ownership belong to the vastbase user
docker_create_db_directories() {
        local user; user="$(id -u)"

        mkdir -p "$PGDATA"
        chmod 700 "$PGDATA"
	chown -R vastbase:vastbase "$GAUSSHOME"
        chown -R vastbase:vastbase "$PGDATA"
        # ignore failure since it will be fine when using the image provided directory;
        mkdir -p /var/run/vastbase || :
        chmod 775 /var/run/vastbase || :

        # Create the transaction log directory before initdb is run so the directory is owned by the correct user
        if [ -n "$POSTGRES_INITDB_XLOGDIR" ]; then
                mkdir -p "$POSTGRES_INITDB_XLOGDIR"
                if [ "$user" = '0' ]; then
                        find "$POSTGRES_INITDB_XLOGDIR" \! -user postgres -exec chown postgres '{}' +
                fi
                chmod 700 "$POSTGRES_INITDB_XLOGDIR"
        fi

        # allow the container to be started with `--user`
        if [ "$user" = '0' ]; then
                find "$PGDATA" \! -user vastbase -exec chown vastbase '{}' +
                find /var/run/vastbase \! -user vastbase -exec chown vastbase '{}' +
        fi
}

# initialize empty PGDATA directory with new database via 'initdb'
# arguments to `initdb` can be passed via POSTGRES_INITDB_ARVB or as arguments to this function
# `initdb` automatically creates the "postgres", "template0", and "template1" dbnames
# this is also where the database user is created, specified by `VB_USER` env
docker_init_database_dir() {
        # "initdb" is particular about the current user existing in "/etc/passwd", so we use "nss_wrapper" to fake that if necessary
        if ! getent passwd "$(id -u)" &> /dev/null && [ -e /usr/lib/libnss_wrapper.so ]; then
                export LD_PRELOAD='/usr/lib/libnss_wrapper.so'
                export NSS_WRAPPER_PASSWD="$(mktemp)"
                export NSS_WRAPPER_GROUP="$(mktemp)"
                echo "postgres:x:$(id -u):$(id -g):PostgreSQL:$PGDATA:/bin/false" > "$NSS_WRAPPER_PASSWD"
                echo "postgres:x:$(id -g):" > "$NSS_WRAPPER_GROUP"
        fi

        if [ -n "$POSTGRES_INITDB_XLOGDIR" ]; then
                set -- --xlogdir "$POSTGRES_INITDB_XLOGDIR" "$@"
        fi

        local initdbCmd='vb_initdb -w $VB_PASSWORD -E UTF8 --locale=en_US.UTF-8  -D $PGDATA'

        if [ -n "$VB_DBCOMPATIBILITY" ]; then
                echo "Specify database compatibility as: "$VB_DBCOMPATIBILITY
                initdbCmd=${initdbCmd}' --dbcompatibility=$VB_DBCOMPATIBILITY'
        else
                echo "Database compatibility uses default values."
        fi
        if [ -n "$VB_NODENAME" ]; then
                initdbCmd=${initdbCmd}' --nodename=$VB_NODENAME'
        else
                initdbCmd=${initdbCmd}' --nodename=vastbase'
        fi

        eval ${initdbCmd}
        # unset/cleanup "nss_wrapper" bits
        if [ "${LD_PRELOAD:-}" = '/usr/lib/libnss_wrapper.so' ]; then
                rm -f "$NSS_WRAPPER_PASSWD" "$NSS_WRAPPER_GROUP"
                unset LD_PRELOAD NSS_WRAPPER_PASSWD NSS_WRAPPER_GROUP
        fi
}

# print large warning if VB_PASSWORD is long
# error if both VB_PASSWORD is empty and VB_HOST_AUTH_METHOD is not 'trust'
# print large warning if VB_HOST_AUTH_METHOD is set to 'trust'
# assumes database is not set up, ie: [ -z "$DATABASE_ALREADY_EXISTS" ]
docker_verify_minimum_env() {
        # check password first so we can output the warning before postgres
        # messes it up
        if [[ "$VB_PASSWORD" =~  ^(.{8,}).*$ ]] &&  [[ "$VB_PASSWORD" =~ ^(.*[a-z]+).*$ ]] && [[ "$VB_PASSWORD" =~ ^(.*[A-Z]).*$ ]] &&  [[ "$VB_PASSWORD" =~ ^(.*[0-9]).*$ ]] && [[ "$VB_PASSWORD" =~ ^(.*[#?!@$%^&*-]).*$ ]]; then
                cat >&2 <<-'EOWARN'

                        Message: The supplied VB_PASSWORD is meet requirements.

EOWARN
        else
                 cat >&2 <<-'EOWARN'

                        Error: The supplied VB_PASSWORD is not meet requirements.
                        Please Check if the password contains uppercase, lowercase, numbers, special characters, and password length(8).
                        At least one uppercase, lowercase, numeric, special character.
                        Example: Enmo@123
EOWARN
       exit 1
        fi
        if [ -z "$VB_PASSWORD" ] && [ 'trust' != "$VB_HOST_AUTH_METHOD" ]; then
                # The - option suppresses leading tabs but *not* spaces. :)
                cat >&2 <<-'EOE'
                        Error: Database is uninitialized and superuser password is not specified.
                               You must specify VB_PASSWORD to a non-empty value for the
                               superuser. For example, "-e VB_PASSWORD=password" on "docker run".

                               You may also use "VB_HOST_AUTH_METHOD=trust" to allow all
                               connections without a password. This is *not* recvastbaseended.

EOE
                exit 1
        fi
        if [ 'trust' = "$VB_HOST_AUTH_METHOD" ]; then
                cat >&2 <<-'EOWARN'
                        ********************************************************************************
                        WARNING: VB_HOST_AUTH_METHOD has been set to "trust". This will allow
                                 anyone with access to the vastbase port to access your database without
                                 a password, even if VB_PASSWORD is set.
                                 It is not recvastbaseended to use VB_HOST_AUTH_METHOD=trust. Replace
                                 it with "-e VB_PASSWORD=password" instead to set a password in
                                 "docker run".
                        ********************************************************************************
EOWARN
        fi
}

# usage: docker_process_init_files [file [file [...]]]
#    ie: docker_process_init_files /always-initdb.d/*
# process initializer files, based on file extensions and permissions
docker_process_init_files() {
        # vsql here for backwards compatiblilty "${vsql[@]}"
        vsql=( docker_process_sql )

        echo
        local f
        for f; do
                case "$f" in
                        *.sh)
                                if [ -x "$f" ]; then
                                        echo "$0: running $f"
                                        "$f"
                                else
                                        echo "$0: sourcing $f"
                                        . "$f"
                                fi
                                ;;
                        *.sql)    echo "$0: running $f"; docker_process_sql -f "$f"; echo ;;
                        *.sql.gz) echo "$0: running $f"; gunzip -c "$f" | docker_process_sql; echo ;;
                        *.sql.xz) echo "$0: running $f"; xzcat "$f" | docker_process_sql; echo ;;
                        *)        echo "$0: ignoring $f" ;;
                esac
                echo
        done
}

# Execute sql script, passed via stdin (or -f flag of pqsl)
# usage: docker_process_sql [vsql-cli-arVB]
#    ie: docker_process_sql --dbname=mydb <<<'INSERT ...'
#    ie: docker_process_sql -f my-file.sql
#    ie: docker_process_sql <my-file.sql
docker_process_sql() {
        local query_runner=( vsql -v ON_ERROR_STOP=1 --username "$VB_USER" --password "$VB_PASSWORD")
        if [ -n "$VB_DB" ]; then
                query_runner+=( --dbname "$VB_DB" )
        fi
        
        echo "Execute SQL: ${query_runner[@]} $@"
        "${query_runner[@]}" "$@"
}

# create initial database
# uses environment variables for input: VB_DB
docker_setup_db() {
        echo "VB_DB = $VB_DB"
        if [ "$VB_DB" != 'vastbase' ]; then
                VB_DB= docker_process_sql --dbname postgres --set db="$VB_DB" --set passwd="$VB_PASSWORD" <<-'EOSQL'
                        CREATE DATABASE :"db" ;
                        create user vastbase with login password :"passwd" ;
                        grant all privileges to vastbase;
                        \i /tmp/Oracle_Functions_1.0.0.sql;
                        \i /tmp/Oracle_Views_1.0.0.sql;

EOSQL
                echo
        fi
}

docker_setup_user() {
        if [ -n "$VB_USERNAME" ]; then
                VB_DB= docker_process_sql --dbname postgres --set db="$VB_DB" --set passwd="$VB_PASSWORD" --set user="$VB_USERNAME" <<-'EOSQL'
                        create user :"user" with login password :"passwd" ;
EOSQL
        else           
                echo " default user is vastbase"
        fi
}


docker_setup_rep_user() {
        if [ -n "$SERVER_MODE" ] && [ "$SERVER_MODE" = "primary" ]; then
                VB_DB= docker_process_sql --dbname postgres --set passwd="RepUser@2020" --set user="repuser" <<-'EOSQL'
                        create user :"user" SYSADMIN REPLICATION password :"passwd" ;
EOSQL
        else           
                echo " default no repuser created"
        fi
}

# 参考 installer\console\installer\install.go
#       生成加密密钥文件
docker_generate_encryption_key_file() {
        # "initdb" is particular about the current user existing in "/etc/passwd", so we use "nss_wrapper" to fake that if necessary
        echo
        echo 'Start generating encryption key file.'
        echo
        eval gs_guc generate -S ${PGPASSWORD} -D $GAUSSHOME/bin -o usermapping
        eval gs_guc generate -S ${PGPASSWORD} -D $GAUSSHOME/bin -o server
        eval gs_guc generate -S ${PGPASSWORD} -D $GAUSSHOME/bin -o obsserver
        echo
        echo 'End generating encryption key file.'
        echo
}

# Loads various settinVB that are used elsewhere in the script
# This should be called before any other functions
docker_setup_env() {
        export VB_USER=vastbase
        file_env 'VB_PASSWORD'

        # file_env 'VB_USER' 'vastbase'
        file_env 'VB_DB' "$VB_USER"
        file_env 'POSTGRES_INITDB_ARVB'
        # default authentication method is md5
        : "${VB_HOST_AUTH_METHOD:=md5}"

        declare -g DATABASE_ALREADY_EXISTS
        # look specifically for OG_VERSION, as it is expected in the DB dir
        if [ -s "$PGDATA/PG_VERSION" ]; then
                DATABASE_ALREADY_EXISTS='true'
        fi
}

# append VB_HOST_AUTH_METHOD to pg_hba.conf for "host" connections
vastbase_setup_hba_conf() {
        {
                echo
                if [ 'trust' = "$VB_HOST_AUTH_METHOD" ]; then
                        echo '# warning trust is enabled for all connections'
                fi
                echo "host all all 0.0.0.0/0 $VB_HOST_AUTH_METHOD"
                echo "host replication vastbase 0.0.0.0/0 md5"
                if [ -n "$SERVER_MODE" ]; then
                    echo "host replication repuser $OG_SUBNET trust"
                fi
        } >> "$PGDATA/pg_hba.conf"
}

vastbase_setup_postgresql_conf_for_225() {
        {
                echo
                if [ -n "$VB_PORT" ]; then
                    echo "password_encryption_type = 0"
                    echo "port = $VB_PORT"
                    echo "wal_level = logical"
                else
                    echo '# use default port 5432'
                    echo "password_encryption_type = 0"
                    echo "wal_level = logical"
                fi
                
                if [ -n "$SERVER_MODE" ]; then
                    echo "listen_addresses = '0.0.0.0'"
                    echo "most_available_sync = on"
                    echo "remote_read_mode = non_authentication"
                    echo "pgxc_node_name = '$NODE_NAME'"
                    # echo "application_name = '$NODE_NAME'"
                    if [ "$SERVER_MODE" = "primary" ]; then
                        echo "max_connections = 100"
                    else
                        echo "max_connections = 100"
                    fi
                    echo -e "$REPL_CONN_INFO"
                    if [ -n "$SYNCHRONOUS_STANDBY_NAMES" ]; then
                        echo "synchronous_standby_names=$SYNCHRONOUS_STANDBY_NAMES"
                    fi
                else
                    echo "license_path='/home/<USER>/vastbase/lic/license'"
                    echo "listen_addresses = '*'"
                    echo "port=5432"
                    echo "maintenance_work_mem=64MB"
                    echo "work_mem=2MB"                    
                    echo "wal_buffers=16MB"
                    echo "archive_mode=on"
                    echo "archive_dest='/home/<USER>/data/archive'"
                    echo "vacuum_cost_limit=10000"
                    echo "autovacuum=on"
                    echo "autovacuum_mode=mix"
                    echo "autovacuum_max_workers=5"
                    echo "autovacuum_naptime=20s"
                    echo "autovacuum_vacuum_cost_delay=10"
                    echo "autovacuum_vacuum_scale_factor=0.02"
                    echo "autovacuum_analyze_scale_factor=0.1"
                    echo "track_sql_count=on"
                    echo "enable_cbm_tracking=on"
                    echo "enable_thread_pool=off"
                    echo "password_encryption_type=0"
                    echo "password_force_alter=off"
                    echo "password_effect_time=36500"
                    echo "remote_read_mode='non_authentication'"
                    echo "enable_resource_track=on"
                    echo "instr_unique_sql_count=20000"
                    echo "enable_wdr_snapshot=on"
                    echo "enable_stmt_track=off"
                    echo "xloginsert_locks=8"
                    echo "log_lock_waits=on"
                    echo "log_statement='ddl'"
                    echo "shared_preload_libraries='pg_stat_statements,decoderbufs'"
                    echo "pg_stat_statements.max=10000"
                    echo "pg_stat_statements.track=all"
                    echo "track_activity_query_size=4096"
                    echo "log_directory='/home/<USER>/data/pg_log'"
                    echo "enable_slot_log = off"
                    echo "walsender_max_send_size = 8MB"
                    echo "enable_kill_query = off"
                    echo "connection_alarm_rate = 0.9"
                    echo "alarm_report_interval = 10"
                    echo "lockwait_timeout = 1200s"
                    echo "job_queue_processes = 10"
                    echo "session_timeout = 0min "
                    echo "client_min_messages = warning"
                    echo "log_min_messages = warning"
                    echo "log_destination = 'stderr'"
                    echo "logging_collector = on"
                    echo "log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'"
                    echo "log_rotation_size = 50MB"
                    echo "log_duration = off"
                    echo "log_file_mode = 0600"
                    echo "log_min_duration_statement = 1800000"
                    echo "log_connections = off"
                    echo "log_disconnections = off"
                    echo "log_hostname = off"
                    echo "log_line_prefix = '%m %r %d %u [%p]'"
                    echo "track_activities = on"
                    echo "enable_instr_track_wait = on"
                    echo "enable_instr_rt_percentile = off"
                    echo "track_counts = on"
                    echo "enable_instr_cpu_timer = off"
                    echo "enable_instance_metric_persistent = off"
                    echo "enable_logical_io_statistics = off"
                    echo "enable_page_lsn_check = off"
                    echo "enable_user_metric_persistent = off"
                    #echo "enable_auto_clean_unique_sql=on"
                    echo "plog_merge_age = 0"
                    echo "max_files_per_process = 100000"
                    echo "max_prepared_transactions = 200"
                    echo "bulk_write_ring_size = 2GB"
                    echo "wal_level = hot_standby"
                    echo "wal_log_hints = on"
                    echo "wal_file_init_num = 20"
                    echo "advance_xlog_file_num = 10"
                    echo "enable_xlog_prune = off"
                    echo "wal_keep_segments = 256"
                    echo "max_replication_slots = 8"
                    echo "max_wal_senders = 4"
                    echo "synchronous_commit=on"
                    echo "synchronous_standby_names='*'"
                    echo "enable_mergejoin = on "
                    echo "enable_nestloop = on "
                    echo "enable_hashjoin = on"
                    echo "enable_bitmapscan = on"
                    echo "enable_material = off"
                    echo "enable_codegen = false"
                    echo "enable_opfusion = off "
                    echo "enable_beta_opfusion=off"
                    echo "query_dop=1"
                    echo "enable_save_datachanged_timestamp = false"
                    echo "autoanalyze_timeout=900  "
                    echo "enable_incremental_checkpoint = on"
                    echo "incremental_checkpoint_timeout = 60s"
                    echo "checkpoint_segments = 128"
                    echo "checkpoint_timeout = 15min"
                    echo "checkpoint_completion_target = 0.9"
                    echo "checkpoint_warning = 5min "
                    echo "checkpoint_wait_timeout = 60s"
                    echo "hot_standby = on"
                    echo "replication_type=1"
                    echo "recovery_max_workers=8"
                    echo "recovery_parallelism=1"
                    echo "recovery_parse_workers=1"
                    echo "recovery_redo_workers=1"
                    echo "recovery_time_target=0"
                    echo "audit_enabled = off"
                    echo "fsync = on"
                    echo "full_page_writes = off"
                    echo "enable_double_write = on "
                    echo "allow_concurrent_tuple_update = true"
                    echo "enable_alarm = off"
                    echo "use_workload_manager = on"
                    echo "transaction_isolation = 'read committed'"
                    echo "default_transaction_isolation = 'read committed'"
                    echo "update_lockwait_timeout = 20min"
                    echo "pagewriter_sleep = 10ms"
                    echo "most_available_sync=on"
                    echo "enable_data_replicate = off"
                    echo "hot_standby_feedback = off"
                    echo "failed_login_attempts=10"
                    echo "datestyle = 'iso, mdy'"
                    echo "timezone = 'PRC'"
                    echo "lc_messages = 'en_US.utf8'"
                    echo "lc_monetary = 'en_US.utf8'"
                    echo "lc_numeric = 'en_US.utf8'"
                    echo "lc_time = 'en_US.utf8'"
                    echo "default_text_search_config = 'pg_catalog.english'"
                    echo "pgxc_node_name = 'node1'"
                    echo "vastbase_login_info=off "
                    echo "max_connections = 1000"
                    echo "temp_file_limit='50GB'"
                    #echo "behavior_compat_options='plsql_security_definer,skip_insert_gs_source'"

                fi

                if [ -n "$OTHER_PG_CONF" ]; then
                    echo -e "$OTHER_PG_CONF"
                fi 

                echo "shared_preload_libraries='jdbc_fdw,pg_stat_statements'"
                echo

        } >> "$PGDATA/postgresql.conf"
}

vastbase_setup_postgresql_conf_for_2210_common() {
        {
                echo
                if [ -n "$VB_PORT" ]; then
                    echo "password_encryption_type = 0"
                    echo "port = $VB_PORT"
                    echo "wal_level = logical"
                else
                    echo '# use default port 5432'
                    echo "password_encryption_type = 0"
                    echo "wal_level = logical"
                fi
                
                if [ -n "$SERVER_MODE" ]; then
                    echo "listen_addresses = '0.0.0.0'"
                    echo "most_available_sync = on"
                    echo "remote_read_mode = non_authentication"
                    echo "pgxc_node_name = '$NODE_NAME'"
                    # echo "application_name = '$NODE_NAME'"
                    if [ "$SERVER_MODE" = "primary" ]; then
                        echo "max_connections = 100"
                    else
                        echo "max_connections = 100"
                    fi
                    echo -e "$REPL_CONN_INFO"
                    if [ -n "$SYNCHRONOUS_STANDBY_NAMES" ]; then
                        echo "synchronous_standby_names=$SYNCHRONOUS_STANDBY_NAMES"
                    fi
                else
                    echo "license_path='/home/<USER>/vastbase/lic/license'"
                    echo "listen_addresses = '*'"
                    echo "port=5432"
                    echo "maintenance_work_mem=64MB"
                    echo "work_mem=2MB"                    
                    echo "wal_buffers=16MB"
                    echo "archive_mode=on"
                    echo "archive_dest='/home/<USER>/data/archive'"
                    echo "vacuum_cost_limit=10000"
                    echo "autovacuum=on"
                    echo "autovacuum_mode=mix"
                    echo "autovacuum_max_workers=5"
                    echo "autovacuum_naptime=20s"
                    echo "autovacuum_vacuum_cost_delay=10"
                    echo "autovacuum_vacuum_scale_factor=0.02"
                    echo "autovacuum_analyze_scale_factor=0.1"
                    echo "track_sql_count=on"
                    echo "enable_cbm_tracking=on"
                    echo "enable_thread_pool=off"
                    echo "password_encryption_type=0"
                    echo "password_force_alter=off"
                    echo "password_effect_time=36500"
                    echo "remote_read_mode='non_authentication'"
                    echo "enable_resource_track=on"
                    echo "instr_unique_sql_count=20000"
                    echo "enable_wdr_snapshot=on"
                    echo "enable_stmt_track=off"
                    echo "xloginsert_locks=8"
                    echo "log_lock_waits=on"
                    echo "log_statement='ddl'"
                    echo "shared_preload_libraries='pg_stat_statements,decoderbufs'"
                    echo "pg_stat_statements.max=10000"
                    echo "pg_stat_statements.track=all"
                    echo "track_activity_query_size=4096"
                    echo "log_directory='/home/<USER>/data/pg_log'"
                    echo "enable_slot_log = off"
                    echo "walsender_max_send_size = 8MB"
                    echo "enable_kill_query = off"
                    echo "connection_alarm_rate = 0.9"
                    echo "alarm_report_interval = 10"
                    echo "vb_wal_directory = 'pg_xlog'"
                    echo "lockwait_timeout = 1200s"
                    echo "job_queue_processes = 10"
                    echo "session_timeout = 0min"
                    echo "client_min_messages = warning"
                    echo "log_min_messages = warning"
                    echo "log_destination = 'stderr'"
                    echo "logging_collector = on"
                    echo "log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'"
                    echo "log_rotation_size = 50MB"
                    echo "log_duration = off"
                    echo "log_file_mode = 0600"
                    echo "log_min_duration_statement = 1800000"
                    echo "log_connections = off"
                    echo "log_disconnections = off"
                    echo "log_hostname = off"
                    echo "log_line_prefix = '%m %r %d %u [%p]'"
                    echo "log_timezone = 'PRC'"
                    echo "track_activities = on"
                    echo "enable_instr_track_wait = on"
                    echo "enable_instr_rt_percentile = off"
                    echo "track_counts = on"
                    echo "enable_instr_cpu_timer = off"
                    echo "enable_instance_metric_persistent = off"
                    echo "enable_logical_io_statistics = off"
                    echo "enable_page_lsn_check = off"
                    echo "enable_user_metric_persistent = off"
                    #echo "enable_auto_clean_unique_sql=on"
                    echo "plog_merge_age = 0"
                    echo "max_files_per_process = 100000"
                    echo "max_prepared_transactions = 200"
                    echo "bulk_write_ring_size = 2GB"
                    echo "wal_level = hot_standby"
                    echo "wal_log_hints = on"
                    echo "wal_file_init_num = 20"
                    echo "advance_xlog_file_num = 10"
                    echo "enable_xlog_prune = off"
                    echo "wal_keep_segments = 256"
                    echo "max_replication_slots = 8"
                    echo "max_wal_senders = 4"
                    echo "synchronous_commit=on"
                    echo "synchronous_standby_names='*'"
                    echo "enable_mergejoin = on "
                    echo "enable_nestloop = on "
                    echo "enable_hashjoin = on"
                    echo "enable_bitmapscan = on"
                    echo "enable_material = off"
                    echo "enable_codegen = false"
                    echo "enable_opfusion = off "
                    echo "enable_beta_opfusion=off"
                    echo "query_dop=1"
                    echo "enable_save_datachanged_timestamp = false"
                    echo "autoanalyze_timeout = 900"
                    echo "enable_incremental_checkpoint = on"
                    echo "incremental_checkpoint_timeout = 60s"
                    echo "checkpoint_segments = 128"
                    echo "checkpoint_timeout = 15min"
                    echo "checkpoint_completion_target = 0.9"
                    echo "checkpoint_warning = 5min "
                    echo "checkpoint_wait_timeout = 60s"
                    echo "hot_standby = on"
                    echo "replication_type=1"
                    echo "recovery_max_workers=8"
                    echo "recovery_parallelism=1"
                    echo "recovery_parse_workers=1"
                    echo "recovery_redo_workers=1"
                    echo "recovery_time_target=0"
                    echo "audit_enabled = off"
                    echo "fsync = on"
                    echo "full_page_writes = off"
                    echo "enable_double_write = on "
                    echo "allow_concurrent_tuple_update = true"
                    echo "enable_alarm = off"
                    echo "use_workload_manager = on"
                    echo "transaction_isolation = 'read committed'"
                    echo "default_transaction_isolation = 'read committed'"
                    echo "update_lockwait_timeout = 20min"
                    echo "pagewriter_sleep = 10ms"
                    echo "most_available_sync = on"
                    echo "enable_data_replicate = off"
                    echo "hot_standby_feedback = off"
                    echo "failed_login_attempts = 10"
                    echo "datestyle = 'iso, mdy'"
                    echo "timezone = 'PRC'"
                    echo "lc_messages = 'en_US.utf8'"
                    echo "lc_monetary = 'en_US.utf8'"
                    echo "lc_numeric = 'en_US.utf8'"
                    echo "lc_time = 'en_US.utf8'"
                    echo "default_text_search_config = 'pg_catalog.english'"
                    echo "pgxc_node_name = 'node1'"
                    echo "vastbase_login_info = off "
                    echo "max_connections = 1000"
                    echo "temp_file_limit = '10GB'"
                    #echo "behavior_compat_options='plsql_security_definer,skip_insert_gs_source'"

                fi

                if [ -n "$OTHER_PG_CONF" ]; then
                    echo -e "$OTHER_PG_CONF"
                fi 

                echo "shared_preload_libraries='jdbc_fdw,pg_stat_statements'"
                echo

        } >> "$PGDATA/postgresql.conf"
}

vastbase_setup_postgresql_conf_for_2210_ge_5() {
        vastbase_setup_postgresql_conf_for_2210_common
        # 还需要增加额外的参数配置
        {
                echo
                echo "track_stmt_stat_level='off,L1'"
                echo "enable_stmt_track=on"
                echo "log_duration=on"
                echo "log_min_duration_statement='3s'"
                echo

        } >> "$PGDATA/postgresql.conf"
}

vastbase_setup_postgresql_conf_for_2210_lt_5() {
        vastbase_setup_postgresql_conf_for_2210_common
}

# append parameter to postgres.conf for connections
vastbase_setup_postgresql_conf() {


        {
                echo
                if [ -n "$VB_PORT" ]; then
                    echo "password_encryption_type = 0"
                    echo "port = $VB_PORT"
                    echo "wal_level = logical"
                else
                    echo '# use default port 5432'
                    echo "password_encryption_type = 0"
                    echo "wal_level = logical"
                fi
                
                if [ -n "$SERVER_MODE" ]; then
                    echo "listen_addresses = '0.0.0.0'"
                    echo "most_available_sync = on"
                    echo "remote_read_mode = non_authentication"
                    echo "pgxc_node_name = '$NODE_NAME'"
                    # echo "application_name = '$NODE_NAME'"
                    if [ "$SERVER_MODE" = "primary" ]; then
                        echo "max_connections = 100"
                    else
                        echo "max_connections = 100"
                    fi
                    echo -e "$REPL_CONN_INFO"
                    if [ -n "$SYNCHRONOUS_STANDBY_NAMES" ]; then
                        echo "synchronous_standby_names=$SYNCHRONOUS_STANDBY_NAMES"
                    fi
                else
	            echo "license_path='/home/<USER>/vastbase/lic/license'"
                    echo "listen_addresses = '*'"
		    echo "port=5432"
                    echo "maintenance_work_mem=64MB"
                    echo "work_mem=2MB"                    
                    echo "wal_buffers=16MB"
                    echo "archive_mode=on"
                    echo "archive_dest='/home/<USER>/data/archive'"
                    echo "vacuum_cost_limit=10000"
                    echo "autovacuum=on"
                    echo "autovacuum_mode=mix"
                    echo "autovacuum_max_workers=5"
                    echo "autovacuum_naptime=20s"
                    echo "autovacuum_vacuum_cost_delay=10"
                    echo "autovacuum_vacuum_scale_factor=0.02"
                    echo "autovacuum_analyze_scale_factor=0.1"
                    echo "track_sql_count=on"
                    echo "enable_cbm_tracking=on"
                    echo "enable_thread_pool=off"
                    echo "password_encryption_type=0"
                    echo "password_force_alter=off"
                    echo "password_effect_time=36500"
                    echo "remote_read_mode='non_authentication'"
                    echo "enable_resource_track=on"
                    echo "instr_unique_sql_count=20000"
                    echo "enable_wdr_snapshot=on"
                    echo "enable_stmt_track=off"
                    echo "xloginsert_locks=8"
                    echo "log_lock_waits=on"
                    echo "log_statement='ddl'"
                    echo "shared_preload_libraries='pg_stat_statements,decoderbufs'"
                    echo "pg_stat_statements.max=10000"
                    echo "pg_stat_statements.track=all"
                    echo "track_activity_query_size=4096"
                    echo "log_directory='/home/<USER>/data/pg_log'"
                    
                fi

                if [ -n "$OTHER_PG_CONF" ]; then
                    echo -e "$OTHER_PG_CONF"
                fi 

                echo "shared_preload_libraries='jdbc_fdw,pg_stat_statements'"
                echo

        } >> "$PGDATA/postgresql.conf"
}

vastbase_setup_mot_conf() {
         echo "enable_numa = false" >> "$PGDATA/mot.conf"
}

# start socket-only postgresql server for setting up or running scripts
# all arguments will be passed along as arguments to `postgres` (via pg_ctl)
docker_temp_server_start() {
        if [ "$1" = 'vastbase' ]; then
                shift
        fi

        # internal start of server in order to allow setup using vsql client
        # does not listen on external TCP/IP and waits until start finishes
        # 使用2.2.13.13766启动数据库时候该参数不允许设置为非空，因此设置为localhost
        set -- "$@" -c listen_addresses='localhost' -p "${PGPORT:-5432}"

        VBUSER="${VBUSER:-$VB_USER}" \
        vb_ctl -D "$PGDATA" \
                -o "$(printf '%q ' "$@")" \
                -w start
}

# stop postgresql server after done setting up user and running scripts
docker_temp_server_stop() {
        VBUSER="${VBUSER:-postgres}" \
        vb_ctl -D "$PGDATA" -m fast -w stop
}

docker_slave_full_backup() {
        vb_ctl build -D "$PGDATA" -b full
}

# check arguments for an option that would cause vastbase to stop
# return true if there is one
docker_setup_slot() {
cp /usr/local/vastbase/wal2json.so /usr/local/vastbase/lib/postgresql
                VB_DB= docker_process_sql --dbname postgres --set db="$VB_DB" --set passwd="$VB_PASSWORD" --set user="$VB_USERNAME" <<-'EOSQL'
                        select * from pg_create_logical_replication_slot('wal2json', 'wal2json');
                        create table vastbase.test (id int primary key, name varchar2(20));
                        insert into vastbase.test values(1,'yun');  
                        insert into vastbase.test values(2,'he');  
                        insert into vastbase.test values(3,'enmo');  
                        ALTER TABLE vastbase.test REPLICA IDENTITY FULL;
EOSQL
}

create_directories_before_initdb() {

        corefile_dir=/tmp/corefile
        if [ ! -d $corefile_dir ]; then
                mkdir $corefile_dir
                echo "directory successfully created: $corefile_dir"
        else
                echo "directory already exists: $corefile_dir"
        fi

        nmon_dir=/home/<USER>/data/nmon
        if [ ! -d $nmon_dir ]; then
                mkdir $nmon_dir
                echo "directory successfully created: $nmon_dir"
        else
                echo "directory already exists: $nmon_dir"
        fi

        archive_dir=/home/<USER>/data/archive
        if [ ! -d $archive_dir ]; then
                mkdir $archive_dir
                echo "directory successfully created: $archive_dir"
        else
                echo "directory already exists: $archive_dir"
        fi

        pg_log_dir=/home/<USER>/data/pg_log
        if [ ! -d $pg_log_dir ]; then
                mkdir $pg_log_dir
                echo "directory successfully created: $pg_log_dir"
        else
                echo "directory already exists: $pg_log_dir"
        fi

        vastbase_log_dir=/home/<USER>/data/vastbase_log
        if [ ! -d $vastbase_log_dir ]; then
                mkdir $vastbase_log_dir
                echo "directory successfully created: $vastbase_log_dir"
        else
                echo "directory already exists: $vastbase_log_dir"
        fi

        data_dir=/home/<USER>/data/vastbase
        if [ ! -d $data_dir ]; then
                mkdir $data_dir
                chmod 0700 $data_dir
                echo "directory successfully created: $data_dir"
        else
                echo "directory already exists: $data_dir"
        fi

        bin_log_dir=$vastbase_log_dir/bin
        if [ ! -d $bin_log_dir ]; then
                mkdir $bin_log_dir
                echo "directory successfully created: $bin_log_dir"
        else
                echo "directory already exists: $bin_log_dir"
        fi

        probackup_result_dir=/home/<USER>/backup/probackup
        if [ ! -d $probackup_result_dir ]; then
                mkdir $probackup_result_dir
                echo "directory successfully created: $probackup_result_dir"
        else
                echo "directory already exists: $probackup_result_dir"
        fi

        probackup_log_dir=/home/<USER>/backup_log/probackup
        if [ ! -d $probackup_log_dir ]; then
                mkdir $probackup_log_dir
                echo "directory successfully created: $probackup_log_dir"
        else
                echo "directory already exists: $probackup_log_dir"
        fi

        dumpall_result_dir=/home/<USER>/backup/dumpall
        if [ ! -d $dumpall_result_dir ]; then
                mkdir $dumpall_result_dir
                echo "directory successfully created: $dumpall_result_dir"
        else
                echo "directory already exists: $dumpall_result_dir"
        fi

        dumpall_log_dir=/home/<USER>/backup_log/dumpall
        if [ ! -d $dumpall_log_dir ]; then
                mkdir $dumpall_log_dir
                echo "directory successfully created: $dumpall_log_dir"
        else
                echo "directory already exists: $dumpall_log_dir"
        fi
}

# 如果是使用docker直接启动镜像，那么 -v ${宿主机目录}:${容器中的目录} 中的宿主机目录user:group都必须是1000:1000
check_permissions() {
        # 目录存在和权限检查（user:group都必须是1000:1000）
        #       目前仅检查需要挂载的目录，里面文件暂时不检查
        res=false
        if [ ! -d "/home/<USER>/data" ]; then 
                echo >&2 "directory does not exist: /home/<USER>/data"
                res=true
        else
                stat_res=`stat -c "%u:%g" /home/<USER>/data`
                if [ ! x"1000:1000" = x${stat_res} ]; then
                        echo >&2 "The directory /home/<USER>/data needs to be mounted, and the user:group in the host must be 1000:1000"
                        echo >&2 "current status is: ${stat_res}"
                        res=true
                else
                        echo "Successfully checked directory /home/<USER>/data"
                fi
        fi

        if [ ! -d "/home/<USER>/vastbase/lic" ]; then 
                echo >&2 "directory does not exist: /home/<USER>/vastbase/lic"
                res=true
        else
                if [ -e "/home/<USER>/vastbase/lic/license" ]; then
                        if [ ! -r "/home/<USER>/vastbase/lic/license" ]; then
                                echo "File does not have read permission: /home/<USER>/vastbase/lic/license"
                                res=true
                        fi
                fi
                if [ -e "/home/<USER>/vastbase/lic/.license" ]; then
                        if [ ! -r "/home/<USER>/vastbase/lic/.license" ]; then
                                echo "File does not have read permission: /home/<USER>/vastbase/lic/.license"
                                res=true
                        fi
                fi
                if [ ${res} != true ]; then
                        echo "Successfully checked directory /home/<USER>/vastbase/lic"
                fi
        fi

        if [ ! -d "/home/<USER>/backup" ]; then 
                echo >&2 "directory does not exist: /home/<USER>/backup"
                res=true
        else
                stat_res=`stat -c "%u:%g" /home/<USER>/backup`
                if [ ! x"1000:1000" = x${stat_res} ]; then
                        echo >&2 "The directory /home/<USER>/backup needs to be mounted, and the user:group in the host must be 1000:1000"
                        echo >&2 "current status is: ${stat_res}"
                        res=true
                else
                        echo "Successfully checked directory /home/<USER>/backup"
                fi
        fi

        if [ ! -d "/home/<USER>/backup_log" ]; then 
                echo >&2 "directory does not exist: /home/<USER>/backup_log"
                res=true
        else
                stat_res=`stat -c "%u:%g" /home/<USER>/backup_log`
                if [ ! x"1000:1000" = x${stat_res} ]; then
                        echo >&2 "The directory /home/<USER>/backup_log needs to be mounted, and the user:group in the host must be 1000:1000"
                        echo >&2 "current status is: ${stat_res}"
                        res=true
                else
                        echo "Successfully checked directory /home/<USER>/backup_log"
                fi
        fi
        if [ ${res} = true ]; then
                echo >&2 "The mount file is not specified or the permissions are wrong. Exit directly."
                exit 3
        fi
}

_vastbase_want_help() {
        local arg
        count=1
        for arg; do
                case "$arg" in
                        # postgres --help | grep 'then exit'
                        # leaving out -C on purpose since it always fails and is unhelpful:
                        # postgres: could not access the server configuration file "/var/lib/postgresql/data/postgresql.conf": No such file or directory
                        -'?'|--help|--describe-config|-V|--version)
                                return 0
                                ;;
                esac
                if [ "$arg" == "-M" ]; then
                        SERVER_MODE=${@:$count+1:1}
                        echo "vastbase DB SERVER_MODE = $SERVER_MODE"
                        shift
                fi
                count=$[$count + 1]
        done
        return 1
}

_main() {
        # if first arg looks like a flag, assume we want to run postgres server
        if [ "${1:0:1}" = '-' ]; then
                set -- vastbase "$@"
        fi

        # 目录存在和权限检查，用户都必须是vastbase
        check_permissions
        create_directories_before_initdb

        if [ "$1" = 'vastbase' ] && ! _vastbase_want_help "$@"; then
                docker_setup_env
                # setup data directories and permissions (when run as root)
                # 依赖的目录将会在dockerfile中创建好
                # docker_create_db_directories

                # 如果以root用户启动，则重新使用vastbase用户启动脚本，因此接下来都是vastbase用户执行
                if [ "$(id -u)" = '0' ]; then
                        # then restart script as postgres user
                        exec gosu vastbase "$BASH_SOURCE" "$@"
                fi

                # only run initialization on an empty data directory
                if [ -z "$DATABASE_ALREADY_EXISTS" ]; then
                        docker_verify_minimum_env

                        # check dir permissions to reduce likelihood of half-initialized database
                        ls /docker-entrypoint-initdb.d/ > /dev/null

                        docker_init_database_dir
                        vastbase_setup_hba_conf
                        res_for_225=`eval 'vastbase --version | grep "vastbase (Vastbase G100 V2\.2 (Build 5\."' || echo "Failed"`
                        res_for_2210=`eval 'vastbase --version | grep "vastbase (Vastbase G100 V2\.2 (Build 10)"' || echo "Failed"`
                        if [[ "X${res_for_225}" != "XFailed" ]]; then
                                # 2.2.5 生产环境内存参数配置
                                echo "Set parameters for 225! "
                                vastbase_setup_postgresql_conf_for_225
                        elif [[ "X${res_for_2210}" != "XFailed" ]]; then
                                # 数据库版本是2.2.10，需要根据commitid判断是否>=2.2.10.5 -- 最佳实践配置有差异
                                res_for_2210_commitid=`vastbase --version | grep "vastbase (Vastbase G100 V2\.2 (Build 10)" | awk -F " commit " '{print $2}' | awk -F " " '{print $1}'`
                                if [[ ! ${res_for_2210_commitid} =~ ^[0-9]+$ ]]; then
                                        # 获取不到 commitid 异常退出
                                        echo "Get commitid failed: "`vastbase --version`
                                        exit 4
                                fi
                                if [[ 10#${res_for_2210_commitid} -ge 10#11033 ]]; then
                                        # 大于等于2.2.10.5的参数设置
                                        vastbase_setup_postgresql_conf_for_2210_ge_5
                                else
                                        # 小于2.2.10.5的参数设置
                                        vastbase_setup_postgresql_conf_for_2210_lt_5
                                fi
                        else
                                # 默认方式
                                vastbase_setup_postgresql_conf
                        fi

                        vastbase_setup_mot_conf

                        # PGPASSWORD is required for vsql when authentication is required for 'local' connections via pg_hba.conf and is otherwise harmless
                        # e.g. when '--auth=md5' or '--auth-local=md5' is used in POSTGRES_INITDB_ARVB
                        # 假设 PGPASSWORD 没有被设置，则使用VB_PASSWORD
                        export PGPASSWORD="${PGPASSWORD:-$VB_PASSWORD}"
                        docker_temp_server_start "$@"
                        if [ -z "$SERVER_MODE" ] || [ "$SERVER_MODE" = "primary" ]; then
                        docker_setup_db
                        docker_setup_user
                        docker_setup_rep_user
                        # docker_setup_slot
                        docker_process_init_files /docker-entrypoint-initdb.d/*
                        fi

                        if [ -n "$SERVER_MODE" ] && [ "$SERVER_MODE" != "primary" ]; then
                            docker_slave_full_backup
                        fi
                        docker_temp_server_stop
                        unset PGPASSWORD

                        echo
                        echo 'vastbase  init process complete; ready for start up.'
                        echo
                else
                        echo
                        echo 'vastbase Database directory appears to contain a database; Skipping initialization'
                        echo
                fi

                export PGPASSWORD="${PGPASSWORD:-$VB_PASSWORD}"
                docker_generate_encryption_key_file
                unset PGPASSWORD
        fi
        exec "$@"
}

if ! _is_sourced; then
        _main "$@"
fi
