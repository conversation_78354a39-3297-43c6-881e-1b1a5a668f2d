
运行原版镜像获取 entrypoint.sh 文件

```bash
# 运行容器
docker run -d --privileged=true -v D:\AppData\Docker\container.d\Vastbase\openGauss_license:/home/<USER>/vastbase/lic -v D:\AppData\Docker\container.d\Vastbase\data:/home/<USER>/data  -v D:\AppData\Docker\container.d\Vastbase\backup:/home/<USER>/backup -v D:\AppData\Docker\container.d\Vastbase\backup-log:/home/<USER>/backup_log  -e VB_PASSWORD=Vastbase@bdtd123 -e VB_USERNAME=postgres -e VB_DBCOMPATIBILITY=PG --name vastbase_g100_test -p 25432:5432 vastbase-g100-single-v1.4-3.0.8.24875-linux-x86_64:20250123144939
# 拷贝文件
docker cp base_postgres:/home/<USER>/scripts/entrypoint.sh ./
```

编译镜像

```bash
# 编译镜像
docker build --progress=plain -t harbor2.qdbdtd.com:8088/middleware/vastbase-g100-single-v1.4-3.0.8.24875-linux-x86_64:20250123144939-bdtd-********** .
docker build --no-cache --progress=plain -t harbor2.qdbdtd.com:8088/middleware/vastbase-g100-single-v1.4-3.0.8.24875-linux-x86_64:20250123144939-bdtd-********** .

docker run -d --privileged=true -v D:\AppData\Docker\container.d\Vastbase\openGauss_license:/home/<USER>/vastbase/lic -v D:\AppData\Docker\container.d\Vastbase\data:/home/<USER>/data  -v D:\AppData\Docker\container.d\Vastbase\backup:/home/<USER>/backup -v D:\AppData\Docker\container.d\Vastbase\backup-log:/home/<USER>/backup_log  -e VB_PASSWORD=Vastbase@bdtd123 -e VB_USERNAME=postgres -e VB_DBCOMPATIBILITY=PG --name vastbase_g100_bdtd_test -p 25432:5432 harbor2.qdbdtd.com:8088/middleware/vastbase-g100-single-v1.4-3.0.8.24875-linux-x86_64:20250123144939-bdtd-**********

docker save harbor2.qdbdtd.com:8088/middleware/vastbase-g100-single-v1.4-3.0.8.24875-linux-x86_64:20250123144939-bdtd-********** -o vastbase-g100-single-v1.4-3.0.8.24875-linux-x86_64_20250123144939-bdtd-**********.tar
docker push harbor2.qdbdtd.com:8088/middleware/vastbase-g100-single-v1.4-3.0.8.24875-linux-x86_64:20250123144939-bdtd-**********
```
