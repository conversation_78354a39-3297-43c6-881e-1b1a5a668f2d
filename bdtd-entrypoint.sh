#!/bin/bash
set -Eeo pipefail

echo "Wrapper Entrypoint: Starting custom permission setup..."
echo "Current user: $(id -u):$(id -g) ($(whoami))"

# Critical: Fix permissions BEFORE calling the base entrypoint
# The base image will check permissions and exit if they're wrong
echo "Setting ownership for mount paths to 1000:1000..."

# Check if we're running as root
if [ "$(id -u)" = '0' ]; then
  echo "Running as root - can modify permissions"
  
  # Fix permissions for all mount directories
  for dir in /home/<USER>/vastbase/lic /home/<USER>/data /home/<USER>/backup /home/<USER>/backup_log; do
    if [ -d "$dir" ]; then
      current_owner=$(stat -c "%u:%g" "$dir" 2>/dev/null || echo "unknown")
      echo "Directory $dir current ownership: $current_owner"
      
      if [ "$current_owner" != "1000:1000" ]; then
        echo "Changing ownership of $dir from $current_owner to 1000:1000"
        chown -R 1000:1000 "$dir" || {
          echo "ERROR: Failed to change ownership of $dir"
          exit 1
        }
        echo "Successfully changed ownership of $dir"
      else
        echo "Directory $dir already has correct ownership (1000:1000)"
      fi
    else
      echo "Warning: Directory $dir does not exist, creating it..."
      mkdir -p "$dir" || {
        echo "ERROR: Failed to create $dir"
        exit 1
      }
      chown -R 1000:1000 "$dir" || {
        echo "ERROR: Failed to set ownership for $dir"
        exit 1
      }
      echo "Created and set ownership for $dir"
    fi
  done
  
  echo "Wrapper Entrypoint: Custom permission setup finished successfully."
else
  echo "ERROR: Not running as root (current user: $(id -u))"
  echo "Cannot modify mount directory permissions."
  echo "Please ensure the container is started with proper privileges."
  exit 1
fi

echo "---------------------------------------------------"

# Use exec to call the original entrypoint script
exec /home/<USER>/scripts/entrypoint.sh "$@"
